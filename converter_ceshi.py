# converter.py
# 功能：高性能大文件转换脚本，专门处理包含大量sheet的Excel文件
# 作者：AI Assistant
# 版本：4.0 (大文件优化版)

import os
import sys
import time
import shutil
import pandas as pd
import win32com.client as win32
import pythoncom
import re
from datetime import datetime
from collections import Counter

class ExcelConverter:
    # 完整中药名录 - 在类开头定义
    CHINESE_HERBS = [
        '阿胶', '艾叶', '安息香',
        '白芷', '白术', '白芍', '白芨', '白蔹', '白薇', '白果', '白头翁', '白附子', '白扁豆', '百合', '百部', '败酱草', '板蓝根', '半夏', '半枝莲', '北沙参', '荜茇', '빗拨', '鳖甲', '槟榔', '薄荷', '补骨脂',
        '苍术', '苍耳子', '侧柏叶', '柴胡', '蟾酥', '常山', '车前子', '车前草', '沉香', '陈皮', '赤芍', '赤小豆', '川贝母', '川楝子', '川牛膝', '川芎', '穿山甲', '穿心莲', '垂盆草', '磁石', '刺五加', '醋柴胡',
        '大黄', '大青叶', '大血藤', '大枣', '代赭石', '丹参', '丹皮', '淡豆豉', '淡竹叶', '胆南星', '当归', '党参', '刀豆', '地肤子', '地骨皮', '地黄', '地龙', '地榆', '颠茄草', '丁香', '冬虫夏草', '冬瓜皮', '冬葵子', '杜仲', '独活',
        '莪术', '鹅不食草','川椒', '利如', '合香', '槐角', '希仙','腹毛', '蚕沙', '五爪龙', '瓜米', '焦四仙各',
        '防风', '防己', '佛手', '茯苓', '浮萍', '附子', '覆盆子','黑老虎', '鸡矢滕', '千斤拔', '龙豆', '九香虫','玉片',
        '干姜', '甘草', '甘遂', '藁本', '葛根', '蛤蚧', '枸杞子', '钩藤', '狗脊', '谷芽', '骨碎补', '瓜蒌', '贯众', '广藿香', '龟甲', '桂枝',
        '海螵蛸', '海藻', '海风藤', '海金沙', '海马', '合欢皮', '何首乌', '荷叶', '核桃仁', '红花', '红参', '红藤', '厚朴', '琥珀', '虎杖', '花椒', '滑石', '怀牛膝', '槐花', '黄柏', '黄精', '黄连', '黄芪', '黄芩', '火麻仁', '藿香',
        '鸡内金', '鸡血藤', '积雪草', '蒺藜', '桔梗', '荆芥', '金钱草', '金银花', '金樱子', '锦灯笼', '韭菜子', '菊花', '决明子', '姜黄', '降香', '僵蚕',
        '诃子', '苦参', '苦杏仁', '款冬花', '昆布','末药', '松针', '肚脐贴', '别甲', '土元','九节','脐贴',
        '莱菔子', '狼毒', '老鹳草', '雷丸', '藜芦', '荔枝核', '连翘', '莲子', '灵芝', '羚羊角', '刘寄奴', '龙胆草', '龙骨', '龙眼肉', '芦根', '芦荟', '鹿茸', '路路通', '罗汉果',
        '马齿苋', '马兜铃', '马钱子', '麦冬', '麦芽', '蔓荆子', '芒硝', '没药', '玫瑰花', '虻虫', '密蒙花', '木蝴蝶', '木香', '木通', '木瓜', '牡蛎',
        '南沙参', '牛蒡子', '牛黄', '牛膝', '女贞子','吴芋', '公英', '紫贝齿', '炒玉女', '瑞香','细石', '黄柏', '杜仲', '泽夕', '桂枝','鸡血藤','山药',
        '藕节','高丽参', '玉荣', '重台', '福临','粉丹', '杞果', '肾四仙各', '浙贝', '焦三仙各','铁朱', '川军', '蝉衣', '鬼箭羽',
        '胖大海', '佩兰', '枇杷叶', '蒲公英', '蒲黄','百倍', '玉米', '高粱', '小麦', '菖蒲','上古', '炒川元', '生晒参10捣碎', '枸杞',
        '蕲蛇', '前胡', '芡实', '羌活', '秦艽', '秦皮', '青蒿', '青皮', '青黛', '全蝎', '拳参','黑附片', '薏米', '鬼箭羽'
        '人参', '肉苁蓉', '肉豆蔻', '肉桂', '乳香','龙豆', '沙棘', '葡萄干', '生晒参', '合香', '三联', '秋蝉',  
        '砂仁', '沙参', '沙苑子', '山药', '山楂', '山茱萸', '商陆', '蛇床子', '蛇莓', '射干', '伸筋草', '升麻', '生地黄', '生姜', '石菖蒲', '石膏', '石斛', '石决明', '使君子', '熟地黄', '水牛角', '水蛭', '丝瓜络', '苏木', '酸枣仁', '锁阳',
        '太子参', '桃仁', '天冬', '天花粉', '天麻', '天南星', '天仙子', '田七', '葶苈子', '通草', '土茯苓', '菟丝子',
        '瓦楞子', '王不留行', '威灵仙', '委陵菜', '乌梅', '乌药', '蜈蚣', '吴茱萸', '五加皮', '五味子', '五倍子',
        '西洋参', '西红花', '细辛', '夏枯草', '仙茅', '仙鹤草', '香附', '香薷', '小茴香', '薤白', '辛夷', '玄参', '续断', '血竭', '血余炭',
        '鸦胆子', '延胡索', '茵陈', '淫羊藿', '银柴胡', '银耳', '益母草', '益智仁', '薏苡仁', '鱼腥草', '玉竹', '郁金', '禹余粮', '远志', '月季花',
        '皂角刺', '泽兰', '泽泻', '知母', '枳壳', '枳实', '栀子', '竹茹', '竹叶', '猪苓', '珍珠母', '赭石', '紫草', '紫花地丁', '紫苏', '紫菀', '自然铜', '棕榈'
        '五灵脂', '黑附片', '龟板', '龙沙', '神曲', '豆豉', '胆星',  '焦渣', '九香虫', '灵脂', '瑞香', '蚕沙', '龙胆', '炒扁豆', 
        '炙甘草', '地龙', '内金', '泽夕', '桂枝','土古','川芎','薄荷',
        
    ]
    
    # 常见的中药炮制前缀和后缀
    HERB_PREFIXES = ['生', '炙', '炒', '蜜', '酒', '醋', '盐', '姜', '麸', '土', '煅', '制', '炮', '蒸', '焙', '烘', '晒']
    HERB_SUFFIXES = ['仁', '子', '花', '叶', '皮', '根', '茎', '果', '核']

    def __init__(self, batch_size=100, max_sheets=None, test_mode=False):
        """
        初始化转换器
        Args:
            batch_size (int): 批处理大小，每处理多少个sheet写入一次文件
            max_sheets (int): 最大处理sheet数量，None表示处理全部
            test_mode (bool): 测试模式，如果为True则只处理前100个sheet
        """
        self.batch_size = batch_size
        self.max_sheets = 100 if test_mode else max_sheets
        self.test_mode = test_mode
        self.processed_count = 0
        self.total_records = 0
        self.total_visits = 0  # 新增：总就诊次数统计
        self.patients_processed = 0  # 新增：处理的患者数量
        self.start_time = None
        self.temp_records = []
        self.extraction_errors = []  # 新增：错误记录

    def safe_extract_cell(self, row, index, default=''):
        """安全提取单元格数据"""
        try:
            if row and len(row) > index and row[index] is not None:
                return str(row[index])
            return default
        except:
            return default

    def extract_address_from_row(self, row):
        """从行数据中提取住址信息"""
        for i, cell in enumerate(row):
            if cell:
                cell_str = str(cell).strip()
                # 检查是否包含地址关键词
                if any(keyword in cell_str for keyword in ['路', '街', '区', '县', '市', '镇', '村', '号', '楼']):
                    return cell_str
        return ''

    def looks_like_prescription_item(self, text):
        """判断文本是否像处方项目 - 使用完整中药名录精确匹配"""
        if not text or len(text) < 1:
            return False
        
        # 首先检查是否是用法用量，如果是则不当作处方
        if self.looks_like_usage(text):
            return False
        
        # 清理文本，去除空格和特殊字符
        clean_text = text.strip()
        
        # 1. 精确匹配完整中药名
        for herb_name in self.CHINESE_HERBS:
            if herb_name in clean_text:
                return True
        
        # 2. 匹配带炮制前缀的中药名
        for prefix in self.HERB_PREFIXES:
            for herb_name in self.CHINESE_HERBS:
                if f"{prefix}{herb_name}" in clean_text:
                    return True
        
        # 3. 匹配中药名+剂量的模式
        for herb_name in self.CHINESE_HERBS:
            # 检查 "药名+数字" 的模式（如：天麻10）
            herb_dosage_pattern = f"{herb_name}\\d+"
            if re.search(herb_dosage_pattern, clean_text):
                return True
        
        # 4. 检查是否是明显的剂量表达（数字+单位）
        dosage_only_pattern = r'^\d+[克gG毫升mlML钱分厘]?$'
        if re.match(dosage_only_pattern, clean_text):
            return True
        
        # 5. 备用：检查是否包含中药特征且符合中药命名规律
        # 这个作为最后的备用判断，以防名录不完整
        if self._is_likely_herb_by_pattern(clean_text):
            return True
            
        return False
    
    def _is_likely_herb_by_pattern(self, text):
        """备用方法：通过模式判断是否可能是中药（以防名录不完整）"""
        if len(text) > 8:  # 太长的文本不太可能是单个药名
            return False
        
        # 常见中药字符特征
        herb_chars = set('草参芪归芍地术苓花子仁皮根叶麻石木珠枣葛桔竹藤丸肉蛇金螂姜甘磁杜天勾巴乌蜣白党生大炙桑苏吉雍膏连芩茵陈栀黄柴胡枳实厚朴夏橘红茯泽泻猪车前通防风荆芥薄荷菊决明骨碎补')
        
        # 检查是否包含中药特征字符
        text_chars = set(text)
        if len(text_chars & herb_chars) >= 1 and len(text) <= 6:
            return True
        
        # 检查是否符合"XX子"、"XX花"、"XX草"等常见中药命名模式
        herb_patterns = [r'.+子$', r'.+花$', r'.+草$', r'.+参$', r'.+芪$', r'.+归$', r'.+术$', r'.+苓$']
        for pattern in herb_patterns:
            if re.match(pattern, text) and len(text) <= 5:
                return True
        
        return False

    def is_prescription_row(self, row):
        """判断是否为处方行 - 改进版本"""
        if not row:
            return False
        
        # 更宽松的判断条件
        prescription_items = 0
        total_non_empty = 0
        
        for cell in row:
            cell_str = self.safe_extract_cell([cell], 0, '').strip()
            if cell_str:
                total_non_empty += 1
                if self.looks_like_prescription_item(cell_str):
                    prescription_items += 1
        
        # 如果非空项目中有至少1个看起来像处方项目，且总项目数>=2，就认为是处方行
        return prescription_items >= 1 and total_non_empty >= 2

    def format_prescription(self, prescription_list):
        """格式化处方信息 - 改进版本"""
        if not prescription_list:
            return ''
        
        # 合并处方行，使用分号分隔不同行，空格分隔同行药物
        formatted_lines = []
        for line in prescription_list:
            if line.strip():
                # 清理格式，但保留更多信息
                cleaned_line = re.sub(r'\s+', ' ', line.strip())
                formatted_lines.append(cleaned_line)
        
        # 使用分号连接不同的处方行，便于区分
        result = '; '.join(formatted_lines)
        
        return result

    def format_usage(self, usage_list):
        """格式化用法用量信息"""
        if not usage_list:
            return ''
        
        # 合并用法用量，使用空格分隔
        formatted_usage = []
        for usage in usage_list:
            if usage.strip():
                formatted_usage.append(usage.strip())
        
        return ' '.join(formatted_usage)

    def extract_patient_data_from_sheet(self, sheet_data, source_excel, sheet_name):
        """
        根据病历卡格式，从单个工作表的数据中提取就诊记录
        增强版本：更好地处理多次就诊、处方和用法用量分离
        """
        all_visits = []
        
        # 检查数据有效性
        if not sheet_data or not isinstance(sheet_data, list) or len(sheet_data) < 1:
            return []

        # 提取第一行的病人基本信息
        base_info = {}
        first_row = sheet_data[0] if sheet_data else []
        
        try:
            # 更安全的数据提取
            base_info['姓名'] = self.safe_extract_cell(first_row, 0, '').strip()
            base_info['性别'] = self.safe_extract_cell(first_row, 1, '').strip()
            base_info['年龄'] = self.safe_extract_cell(first_row, 2, '').strip()
            base_info['住址'] = self.safe_extract_cell(first_row, 3, '').strip()  # 第4列改为住址
            base_info['联系方式'] = self.safe_extract_cell(first_row, 4, '').strip()
            
        except Exception as e:
            error_msg = f"提取基本信息失败 - Sheet: {sheet_name}, 错误: {str(e)}"
            self.extraction_errors.append(error_msg)
            return []

        # 验证必要信息
        if not base_info.get('姓名') or base_info['姓名'] in ['None', 'nan', '']:
            return []

        # 解析多次就诊记录 - 改进版本
        current_visit = None
        date_pattern = r'^\d{4}[./-]\d{1,2}[./-]\d{1,2}$'
        partial_date_pattern = r'^\d{1,2}[./-]\d{1,2}$'  # 支持月.日格式
        
        for row_idx, row in enumerate(sheet_data[1:], start=1):
            if not row or not any(str(cell).strip() for cell in row if cell):
                continue

            first_cell = self.safe_extract_cell(row, 0, '').strip()
            
            # 检查完整日期格式或部分日期格式
            date_match = re.match(date_pattern, first_cell) or re.match(partial_date_pattern, first_cell)
            
            if date_match:
                # 保存前一次就诊记录
                if current_visit:
                    current_visit['处方'] = self.format_prescription(current_visit.get('处方_list', []))
                    current_visit['用法用量'] = self.format_usage(current_visit.get('用法用量_list', []))
                    current_visit.pop('处方_list', None)
                    current_visit.pop('用法用量_list', None)
                    all_visits.append(current_visit)

                # 开始新的就诊记录
                current_visit = base_info.copy()
                current_visit['就诊时间'] = first_cell
                
                # 提取症状、处方和用法用量信息（日期同行的其他列）
                symptoms = []
                prescription_in_date_row = []
                usage_in_date_row = []
                
                for i in range(1, len(row)):
                    cell_value = self.safe_extract_cell(row, i, '').strip()
                    if cell_value:
                        if self.looks_like_usage(cell_value):  # 1. 先判断用法用量
                            usage_in_date_row.append(cell_value)
                        elif self.looks_like_prescription_item(cell_value):  # 2. 再判断处方
                            prescription_in_date_row.append(cell_value)
                        elif self.looks_like_symptom(cell_value):  # 3. 最后判断症状
                            symptoms.append(cell_value)
                        else:
                            # 默认情况下，当作症状
                            symptoms.append(cell_value)
                
                current_visit['症状诊断'] = '，'.join(symptoms) if symptoms else ''  # 用中文逗号连接症状
                current_visit['处方_list'] = []
                current_visit['用法用量_list'] = []
                
                # 如果日期行包含处方或用法用量信息，添加到相应列表
                if prescription_in_date_row:
                    current_visit['处方_list'].append('   '.join(prescription_in_date_row))
                if usage_in_date_row:
                    current_visit['用法用量_list'].append('   '.join(usage_in_date_row))
                    
                current_visit['来源文件'] = f"{source_excel}#{sheet_name}"

            elif current_visit:
                # 处理后续行 - 区分症状续行、处方行和用法用量行
                prescription_line = []
                usage_line = []
                symptom_line = []
                has_prescription_items = False
                has_symptoms = False
                
                for cell in row:
                    cell_value = self.safe_extract_cell([cell], 0, '').strip()
                    if cell_value:
                        if self.looks_like_usage(cell_value):  # 1. 先判断用法用量
                            usage_line.append(cell_value)
                        elif self.looks_like_prescription_item(cell_value):  # 2. 再判断处方
                            prescription_line.append(cell_value)
                            has_prescription_items = True
                        elif self.looks_like_symptom(cell_value):  # 3. 最后判断症状
                            symptom_line.append(cell_value)
                            has_symptoms = True
                        else:
                            # 如果无法明确判断，根据上下文决定
                            if has_symptoms or len(symptom_line) > 0:
                                symptom_line.append(cell_value)
                            else:
                                prescription_line.append(cell_value)
                
                # 添加症状续行（如果有）
                if symptom_line:
                    existing_symptoms = current_visit.get('症状诊断', '')
                    new_symptoms = '，'.join(symptom_line)
                    if existing_symptoms:
                        current_visit['症状诊断'] = existing_symptoms + '，' + new_symptoms
                    else:
                        current_visit['症状诊断'] = new_symptoms
                
                # 添加处方信息
                if prescription_line and (has_prescription_items or len(prescription_line) >= 2):
                    current_visit['处方_list'].append('   '.join(prescription_line))
                
                # 添加用法用量信息
                if usage_line:
                    current_visit['用法用量_list'].append('   '.join(usage_line))

        # 保存最后一次就诊记录
        if current_visit:
            current_visit['处方'] = self.format_prescription(current_visit.get('处方_list', []))
            current_visit['用法用量'] = self.format_usage(current_visit.get('用法用量_list', []))
            current_visit.pop('处方_list', None)
            current_visit.pop('用法用量_list', None)
            all_visits.append(current_visit)

        # 统计信息
        if all_visits:
            self.patients_processed += 1
            self.total_visits += len(all_visits)

        return all_visits

    def save_batch_to_csv(self, output_file, records, is_first_batch=False):
        """
        将一批记录保存到CSV文件 - 改进版本
        """
        if not records:
            return
            
        df = pd.DataFrame(records)
        
        # 标准化列顺序 - 删除医保状态，添加用法用量
        column_order = ['姓名', '性别', '年龄', '住址', '联系方式', '就诊时间', '症状诊断', '处方', '用法用量', '来源文件']
        
        # 确保所有列都存在
        for col in column_order:
            if col not in df.columns:
                df[col] = ''
        
        df = df.reindex(columns=column_order)
        
        # 数据清理
        for col in df.columns:
            df[col] = df[col].fillna('').astype(str)
        
        # 第一批写入时包含标题，后续批次追加不含标题
        mode = 'w' if is_first_batch else 'a'
        header = is_first_batch
        
        df.to_csv(output_file, index=False, encoding='utf-8-sig', mode=mode, header=header)
        
    def estimate_remaining_time(self, processed, total):
        """
        估算剩余处理时间
        """
        if processed == 0:
            return "计算中..."
            
        elapsed = time.time() - self.start_time
        rate = processed / elapsed
        remaining = (total - processed) / rate
        
        hours = int(remaining // 3600)
        minutes = int((remaining % 3600) // 60)
        seconds = int(remaining % 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"

    def print_progress(self, current, total, records_count):
        """
        打印详细进度信息 - 增强版本
        """
        percentage = (current / total) * 100
        remaining_time = self.estimate_remaining_time(current, total)
        elapsed = time.time() - self.start_time
        
        progress_bar = "█" * int(percentage // 2) + "░" * (50 - int(percentage // 2))
        
        print(f"\r[{progress_bar}] {percentage:.2f}% | "
              f"Sheet: {current}/{total} | "
              f"患者: {self.patients_processed} | "
              f"就诊: {self.total_visits} | "
              f"记录: {records_count} | "
              f"已用时: {elapsed:.0f}s | "
              f"预计剩余: {remaining_time}", end="")

    def print_statistics(self):
        """打印详细统计信息"""
        print(f"\n📊 详细统计信息:")
        print(f"   👥 处理患者数量: {self.patients_processed}")
        print(f"   🏥 总就诊次数: {self.total_visits}")
        print(f"   📝 总记录数: {self.total_records}")
        if self.patients_processed > 0:
            avg_visits = self.total_visits / self.patients_processed
            print(f"   📈 平均就诊次数: {avg_visits:.2f}")
        
        if self.extraction_errors:
            print(f"   ⚠️  提取错误数: {len(self.extraction_errors)}")

    def looks_like_usage(self, text):
        """判断文本是否像用法用量 - 基于具体用法用量表达"""
        if not text:
            return False
        
        # 精确的用法用量表达 - 根据实际统计结果
        exact_usage_expressions = [
            '付代煎', '一付', '付冲剂','颗粒剂','袋/付','次/付/日',
            # 可以继续添加你发现的其他用法用量表达
        ]
        
        # 首先检查精确匹配
        clean_text = text.strip()
        for expression in exact_usage_expressions:
            if expression in clean_text:
                return True
        
        # 用法用量的关键词模式
        usage_keywords = [
            '代煎', '冲剂', 
            '日服', '每日', '早晚', '饭前', '饭后', 
            '空腹', '睡前', '温服', '凉服', '分服', '顿服', '冲服',
            '煎服', '水煎', '先煎', '后下', '包煎', '另煎', '兑服'
        ]
        
        # 检查是否包含用法用量关键词
        for keyword in usage_keywords:
            if keyword in clean_text:
                return True
        
        # 用法用量的数字模式 - 修改为更精确的模式
        usage_patterns = [
            r'\d+付',          # 如：5付、10付
            r'\d+剂',          # 如：3剂、7剂  
            r'每日\d+次',      # 如：每日2次、每日3次 (更精确)
            r'日服\d+次',      # 如：日服2次、日服3次
            r'一日\d+次服',    # 如：一日2次服、一日3次服
            r'\d+次服用',      # 如：2次服用、3次服用
            r'\d+袋',          # 如：1袋、2袋
            r'\d+付代煎',      # 如：5付代煎
            r'\d+袋/付',       # 如：1袋/付
            r'\d+付冲剂',      # 如：3付冲剂
        ]
        
        # 检查是否匹配用法用量数字模式
        for pattern in usage_patterns:
            if re.search(pattern, clean_text):
                return True
        
        return False

    def looks_like_symptom(self, text):
        """判断文本是否像症状描述 - 排除用法用量后的症状识别"""
        if not text or len(text) < 2:
            return False
        
        # 明确的症状描述关键词（避免与用法用量混淆）
        symptom_keywords = [
            # 症状动词
            '难', '差', '痛', '疼', '酸', '胀', '闷', '憋', '堵', '塞', '痒', '麻', '凉', '热',
            '干', '湿', '出汗', '盗汗', '自汗', '失眠', '多梦', '易醒', '早醒', '不适',
            '缓解', '好转', '无缓解', '加重', '减轻',
            
            # 身体部位
            '头', '眼', '耳', '鼻', '口', '咽', '喉', '胸', '背', '腰', '腹', '胃', 
            '肚', '手', '足', '腿', '膝', '肩', '颈', '脚心', '小腿', '乳房', '牙',
            
            # 中医脉象舌象  
            '脉沉', '脉浮', '脉数', '脉迟', '脉细', '脉弦', '脉滑', '脉洪', '脉弱',
            '舌淡', '舌红', '舌质', '舌体', '舌边', '舌尖', '舌根', '苔白', '苔黄', '苔厚', '苔薄',
            
            # 大小便相关 - 扩充更多相关词汇
            '大便', '小便', '尿频', '尿急', '便秘', '便溏', '便血', '不成形', '成形',
            '粘', '不粘', '便前', '便后', '排便', '尿',
            
            # 食物消化
            '纳差', '纳呆', '纳可', '食欲', '腹胀', '腹满', '消化不良', '积食', '胃胀',
            
            # 情绪状态  
            '烦躁', '易怒', '忧郁', '焦虑', '恐惧', '善惊', '心悸', '眠差', '眠浅', '眠可',
            
            # 其他症状
            '乏力', '疲劳', '头晕', '头痛', '气短', '咳嗽', '咳痰', '喘息', '脱发',
            '血压', 'BP', '头懵', '口苦', '口腔溃疡', '三高', '乳腺增生', '甲状腺结节',
            '血脂', '肾结石', '高血压', '胆囊炎', '调经', '周期', '排卵期', '出血',
            
            # 测量值相关
            'mmhg', 'mmHg', '测'
        ]
        
        # 检查是否包含明确的症状关键词
        for keyword in symptom_keywords:
            if keyword in text:
                return True
        
        # 特殊模式：包含"大便"、"小便"等生理描述的文本应该被识别为症状
        physiological_patterns = [
            r'大便.*日.*次',     # 如：大便1日1次、大便一日3-4次
            r'小便.*日.*次',     # 如：小便1日2次
            r'.*日.*次.*便',     # 如：1日1次大便
            r'.*日.*次.*尿',     # 如：1日3次小便
        ]
        
        for pattern in physiological_patterns:
            if re.search(pattern, text):
                return True
        
        # 症状描述通常较长且包含标点符号
        if ('，' in text or '。' in text) and len(text) > 6:
            # 但要排除可能是用法用量的情况
            if not any(usage_word in text for usage_word in ['付', '剂', '代煎', '冲剂', '服用']):
                return True
            
        # 长文本且不含数字和用法用量词汇
        if (len(text) > 8 and 
            not any(usage_word in text for usage_word in ['付', '剂', '代煎', '冲剂', '服用'])):
            return True
            
        return False

    def debug_text_classification(self, text):
        """调试方法：显示文本的分类结果"""
        print(f"文本: '{text}'")
        print(f"  用法用量: {self.looks_like_usage(text)}")
        print(f"  处方药物: {self.looks_like_prescription_item(text)}")
        print(f"  症状描述: {self.looks_like_symptom(text)}")
        print("-" * 30)

    def convert_excel_file(self, file_path):
        """
        主转换函数 - 优化版本
        """
        if not os.path.isfile(file_path):
            print(f"错误：文件不存在 -> {file_path}")
            return False

        print(f"开始处理大文件: {os.path.basename(file_path)}")
        if self.test_mode:
            print(f"🧪 测试模式：只处理前 {self.max_sheets} 个工作表")
        print(f"批处理大小: {self.batch_size} sheets")
        self.start_time = time.time()

        # 准备输出文件
        dir_name = os.path.dirname(file_path)
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        
        # 测试模式下文件名添加test标识
        if self.test_mode:
            output_file = os.path.join(dir_name, f"{base_name}_test_demo.csv")
        else:
            output_file = os.path.join(dir_name, f"{base_name}_converted.csv")
        
        # 如果输出文件已存在，先备份
        if os.path.exists(output_file):
            backup_name = f"{output_file}.backup_{int(time.time())}"
            shutil.move(output_file, backup_name)
            print(f"原有输出文件已备份为: {os.path.basename(backup_name)}")

        # 初始化 COM
        pythoncom.CoInitialize()
        excel_app = None
        is_first_batch = True
        
        try:
            # 启动 Excel
            print("\n正在启动 Excel 应用...")
            excel_app = win32.Dispatch("Excel.Application")
            excel_app.Visible = False
            excel_app.DisplayAlerts = False

            print("正在打开工作簿... (大文件可能需要较长时间)")
            workbook = excel_app.Workbooks.Open(os.path.abspath(file_path), ReadOnly=True)
            
            total_sheets = workbook.Worksheets.Count
            
            # 确定实际要处理的sheet数量
            sheets_to_process = min(total_sheets, self.max_sheets) if self.max_sheets else total_sheets
            
            print(f"\n文件打开成功！")
            print(f"文件总共有 {total_sheets} 个工作表")
            if self.test_mode:
                print(f"🧪 测试模式：将处理前 {sheets_to_process} 个工作表")
            else:
                print(f"将处理 {sheets_to_process} 个工作表")
            print(f"预计处理时间: 约 {sheets_to_process // 10} 分钟 (取决于数据复杂度)")
            print("=" * 70)

            for i in range(1, sheets_to_process + 1):
                try:
                    sheet = workbook.Worksheets(i)
                    sheet_data_tuple = sheet.UsedRange.Value
                    
                    # 添加调试信息
                    print(f"\n正在处理第{i}个工作表: {sheet.Name}")
                    if sheet_data_tuple and len(sheet_data_tuple) > 0:
                        first_row = sheet_data_tuple[0]
                        if first_row and len(first_row) > 0:
                            print(f"  患者姓名: {first_row[0]}")
                    
                    if sheet_data_tuple:
                        # 安全地转换数据格式
                        if isinstance(sheet_data_tuple[0], (list, tuple)):
                            sheet_data_list = [list(row) for row in sheet_data_tuple]
                        else:
                            # 单行数据的特殊处理
                            sheet_data_list = [list(sheet_data_tuple)]
                            
                        sheet_records = self.extract_patient_data_from_sheet(
                            sheet_data_list, os.path.basename(file_path), sheet.Name
                        )
                        
                        if sheet_records:
                            self.temp_records.extend(sheet_records)
                            self.total_records += len(sheet_records)

                except Exception as e:
                    print(f"\n警告: Sheet {i} 处理失败 ({e})，已跳过")
                    continue

                self.processed_count = i
                
                # 显示进度
                self.print_progress(i, sheets_to_process, self.total_records)
                
                # 批量保存
                if len(self.temp_records) >= self.batch_size or i == sheets_to_process:
                    if self.temp_records:
                        self.save_batch_to_csv(output_file, self.temp_records, is_first_batch)
                        is_first_batch = False
                        self.temp_records.clear()  # 释放内存

            workbook.Close(SaveChanges=False)
            print(f"\n\n✅ 数据提取完成！")
            print(f"📊 总计处理了 {sheets_to_process} 个工作表")
            print(f"📝 提取了 {self.total_records} 条就诊记录")
            
            if self.test_mode:
                print(f"🧪 这是测试模式的结果，如果满意请将test_mode设为False进行完整转换")

        except Exception as e:
            print(f"\n❌ 处理过程中发生错误: {e}")
            print("\n请检查:")
            print("1. Microsoft Excel 是否已正确安装")
            print("2. 文件是否被其他程序占用")
            print("3. 磁盘空间是否充足")
            print("4. 文件是否损坏")
            return False
            
        finally:
            if excel_app:
                excel_app.Quit()
            pythoncom.CoUninitialize()

        # 最终统计
        if self.total_records > 0:
            print(f"\n🎉 转换成功完成！")
            print(f"📄 输出文件: {output_file}")
            print(f"📊 文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
            
            # 测试模式不备份原始文件
            if not self.test_mode:
                try:
                    backup_dir = os.path.join(dir_name, "backup_originals")
                    os.makedirs(backup_dir, exist_ok=True)
                    backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                    if not os.path.exists(backup_path):
                        shutil.copy2(file_path, backup_path)
                        print(f"📁 原始文件已备份到: {backup_dir}")
                except Exception as e:
                    print(f"⚠️  备份原始文件失败: {e}")

            # 打印详细统计信息
            self.print_statistics()

            # 显示错误报告
            if self.extraction_errors:
                print(f"\n⚠️  处理过程中遇到 {len(self.extraction_errors)} 个错误:")
                for error in self.extraction_errors[:5]:  # 只显示前5个错误
                    print(f"   - {error}")
                if len(self.extraction_errors) > 5:
                    print(f"   ... 还有 {len(self.extraction_errors) - 5} 个错误")

        else:
            print("\n❌ 未提取到任何有效数据")
            return False

        total_time = time.time() - self.start_time
        print(f"⏱️  总耗时: {total_time / 60:.2f} 分钟")
        print(f"⚡ 平均速度: {sheets_to_process / total_time:.2f} sheets/秒")
        
        return True


# ==============================================================================
# --- 程序主入口 ---
# ==============================================================================
if __name__ == "__main__":
    # --- 请在这里修改为您文件的实际完整路径 ---
    FILE_PATH_TO_CONVERT = r'E:\code\yiyaoxinxichuli\records\realdata712.xlsx'  # <--- 修改这里
    
    # 可调参数
    BATCH_SIZE = 50      # 每批处理的sheet数量
    TEST_MODE = False     # 🧪 测试模式：True=只处理前100个sheet，False=处理全部
    
    # ----------------------------------------------------
    
    print("=" * 70)
    print("🚀 医疗数据高性能转换工具 v4.0")
    print("=" * 70)
    print(f"📂 目标文件: {FILE_PATH_TO_CONVERT}")
    print(f"⚙️  批处理大小: {BATCH_SIZE}")
    if TEST_MODE:
        print("🧪 当前为测试模式：只处理前100个工作表")
        print("   如果测试成功，请将 TEST_MODE 改为 False 进行完整转换")
    print("=" * 70)
    
    converter = ExcelConverter(batch_size=BATCH_SIZE, test_mode=TEST_MODE)
    success = converter.convert_excel_file(FILE_PATH_TO_CONVERT)
    
    if success:
        if TEST_MODE:
            print("\n🎊 测试转换完成！")
            print("✅ 请检查生成的 *_test_demo.csv 文件")
            print("✅ 如果结果满意，请将代码中的 TEST_MODE 改为 False 进行完整转换")
        else:
            print("\n🎊 完整转换任务已完成！您现在可以使用生成的CSV文件进行后续处理。")
    else:
        print("\n💥 转换失败，请检查错误信息并重试。")
    
    input("\n按 Enter 键退出...")